import React from 'react';
import { Link } from 'react-router-dom';
import { jobsData } from '../data/jobsData';

const Experience = () => {
  const handleCompanyLinkClick = (e) => {
    // Prevent the parent Link from being triggered when clicking the company link
    e.stopPropagation();
  };

  return (
    <section className="experience">
      <h2>Professional Experience</h2>
      <div className="timeline">
        {jobsData.map((job, index) => (
          <div key={job.id} className="timeline-item">
            <div className="timeline-dot"></div>
            <Link to={`/job/${job.slug}`} className="timeline-content-link">
              <div className="timeline-content">
                <img
                  src={job.logo}
                  alt={job.logoAlt}
                  className="company-logo"
                />
                <h3 className="job-title">{job.title}</h3>
                <h4 className="company-name">{job.company}</h4>
                {job.companyLink && (
                  <p className="company-link">
                    <a
                      href={job.companyLink}
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={handleCompanyLinkClick}
                    >
                      {job.companyLink}
                    </a>
                  </p>
                )}
                <p className="job-duration">{job.duration}</p>
                <p className="job-description">{job.summary}</p>
                <div className="view-details">
                  <span>View Details →</span>
                </div>
              </div>
            </Link>
          </div>
        ))}
      </div>
    </section>
  );
};

export default Experience;
